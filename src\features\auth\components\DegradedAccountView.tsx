// src/features/auth/components/DegradedAccountView.tsx
// Componente que se muestra a usuarios degradados (ex-usuarios de pago)

'use client';

import Link from 'next/link';
import { FiLock, FiArrowRight, FiClock, FiRefreshCw } from 'react-icons/fi';
import { useState } from 'react';

interface DegradedAccountViewProps {
  onRefresh?: () => void;
}

export default function DegradedAccountView({ onRefresh }: DegradedAccountViewProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (onRefresh) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
  };

  return (
    <div className="min-h-[calc(100vh-200px)] flex flex-col items-center justify-center text-center p-6 bg-gray-50">
      <div className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg p-8 md:p-12">
        {/* Icono principal */}
        <div className="mb-8">
          <div className="w-20 h-20 mx-auto bg-orange-100 rounded-full flex items-center justify-center">
            <FiLock className="w-10 h-10 text-orange-600" />
          </div>
        </div>

        {/* Título y mensaje principal */}
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Tu suscripción ha finalizado
        </h1>
        
        <p className="text-lg text-gray-600 mb-8 leading-relaxed">
          Para acceder a tus <strong>planes de estudio</strong>, <strong>resúmenes</strong>, 
          <strong> tests</strong>, <strong>flashcards</strong> y todas las herramientas de IA, 
          necesitas reactivar tu suscripción.
        </p>

        {/* Beneficios de reactivar */}
        <div className="bg-blue-50 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            Al reactivar tu plan tendrás acceso inmediato a:
          </h3>
          <ul className="text-left text-blue-800 space-y-2">
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              Todo tu material generado previamente
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              Generación ilimitada de tests y flashcards
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              Planes de estudio personalizados con IA
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              Chat inteligente con tu preparador virtual
            </li>
          </ul>
        </div>

        {/* Botones de acción */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            href="/upgrade-plan"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-green-700 text-white font-semibold rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <span>Reactiva tu Plan Premium</span>
            <FiArrowRight className="w-5 h-5 ml-2" />
          </Link>

          {onRefresh && (
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 transition-colors duration-200 disabled:opacity-50"
            >
              <FiRefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Verificando...' : 'Verificar Estado'}
            </button>
          )}
        </div>

        {/* Mensaje de garantía */}
        <div className="mt-8 p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center justify-center text-green-800">
            <FiClock className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">
              Reactivación instantánea - Acceso inmediato a todo tu contenido
            </span>
          </div>
        </div>

        {/* Información adicional */}
        <p className="text-sm text-gray-500 mt-6">
          ¿Tienes problemas? <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">Contacta con soporte</a>
        </p>
      </div>
    </div>
  );
}
