/**
 * Cliente de Gemini - Migrado a OpenAI
 *
 * Este archivo actúa como un proxy para mantener la compatibilidad con el código existente
 * mientras usa OpenAI en lugar de Vertex AI.
 */

// Importar el cliente de OpenAI
import {
  truncarContenido as truncarContenidoOpenAI,
  prepararDocumentos as prepararDocumentosOpenAI,
} from '../openai/openaiClient';

// Re-exportar las funciones para mantener compatibilidad con el código existente
export const model = {
  generateContent: async (_prompt: string) => {
    // ❌ ESTE MÉTODO ESTÁ OBSOLETO - Usar servicios específicos con configuración propia
    throw new Error('❌ MÉTODO OBSOLETO: No usar model.generateContent. Cada servicio debe usar su configuración específica con getOpenAIConfig()');
  }
};

export const truncarContenido = truncarContenidoOpenAI;
export const prepararDocumentos = prepararDocumentosOpenAI;

// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado
// Todas las funciones de procesamiento de documentos y chunking ahora se manejan en openaiClient.ts
