'use client';

import React, { useState } from 'react';
import { FiSearch, FiUser } from 'react-icons/fi';

export default function UserLookupWidget() {
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResult(null);
    try {
      const res = await fetch(`/api/admin/user-lookup?query=${query}`);
      const data = await res.json();
      setResult(data);
    } catch (err) {
      setResult({ error: 'Error de conexión' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center mb-4">
        <FiSearch className="w-6 h-6 text-gray-500 mr-3" />
        <h3 className="text-lg font-semibold text-gray-800">Buscar Usuario</h3>
      </div>
      <form onSubmit={handleSearch} className="flex gap-2">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Email o User ID..."
          className="flex-grow px-3 py-2 border border-gray-300 rounded-md"
        />
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-800 disabled:opacity-50"
        >
          {loading ? '...' : 'Buscar'}
        </button>
      </form>
      {result && (
        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <pre className="text-xs overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
