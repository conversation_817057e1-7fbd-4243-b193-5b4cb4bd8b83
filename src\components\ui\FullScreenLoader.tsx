// src/components/ui/FullScreenLoader.tsx
// Componente de carga a pantalla completa reutilizable

'use client';

import { FiLoader } from 'react-icons/fi';

interface FullScreenLoaderProps {
  message?: string;
  subMessage?: string;
}

export default function FullScreenLoader({ 
  message = "Cargando...", 
  subMessage 
}: FullScreenLoaderProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
          <FiLoader className="absolute inset-0 m-auto w-6 h-6 text-blue-600 animate-pulse" />
        </div>
        <h3 className="mt-6 text-lg font-medium text-gray-900">{message}</h3>
        {subMessage && (
          <p className="mt-2 text-sm text-gray-600">{subMessage}</p>
        )}
      </div>
    </div>
  );
}
