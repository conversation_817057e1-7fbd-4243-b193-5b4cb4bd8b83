'use client';

import { useTokenThresholdNotifier } from '@/hooks/useTokenThresholdNotifier';

/**
 * Componente "manager" que activa los hooks de notificación globales
 * para la aplicación. No renderiza ninguna UI.
 */
export default function NotificationManager() {
  // Activa el hook que escucha los cambios en el uso de tokens y muestra notificaciones.
  useTokenThresholdNotifier();

  // No renderiza nada. Su propósito es puramente de gestión de estado/efectos.
  return null;
}
