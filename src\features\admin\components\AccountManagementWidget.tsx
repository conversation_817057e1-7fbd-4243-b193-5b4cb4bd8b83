'use client';

import React, { useState, useEffect } from 'react';
import { FiU<PERSON>s, FiClock, FiTrash2 } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

export default function AccountManagementWidget() {
  const [freeStats, setFreeStats] = useState({ active: 0, expired: 0 });
  const [graceStats, setGraceStats] = useState({ activeGracePeriods: { total: 0 }, expiredPendingProcessing: { total: 0 } });
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [freeRes, graceRes] = await Promise.all([
        fetch('/api/admin/cleanup-expired-free'),
        fetch('/api/admin/process-expired-grace-periods'),
      ]);
      const freeData = await freeRes.json();
      const graceData = await graceRes.json();
      if (freeRes.ok) setFreeStats(freeData.statistics);
      if (graceRes.ok) setGraceStats(graceData.statistics);
    } catch (error) {
      console.error("Error fetching account stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (endpoint: string, successMessage: string) => {
    const toastId = toast.loading('Ejecutando acción...');
    try {
      const res = await fetch(endpoint, { method: 'POST' });
      const data = await res.json();
      if (res.ok) {
        toast.success(successMessage, { id: toastId });
        fetchData();
      } else {
        toast.error(data.error || 'Error en la acción.', { id: toastId });
      }
    } catch (error) {
      toast.error('Error de conexión.', { id: toastId });
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center mb-4">
        <FiUsers className="w-6 h-6 text-gray-500 mr-3" />
        <h3 className="text-lg font-semibold text-gray-800">Gestión de Cuentas</h3>
      </div>
      {loading ? (
        <p>Cargando datos...</p>
      ) : (
        <div className="space-y-6">
          {/* Cuentas en Periodo de Gracia */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Periodos de Gracia</h4>
            <div className="flex justify-between items-center bg-yellow-50 p-3 rounded-md">
              <p className="text-sm text-yellow-800">Usuarios con suscripción cancelada pendientes de degradar.</p>
              <span className="text-xl font-bold text-yellow-900">{graceStats.expiredPendingProcessing.total}</span>
            </div>
            <button
              onClick={() => handleAction('/api/admin/process-expired-grace-periods', 'Periodos de gracia procesados.')}
              className="mt-2 w-full text-sm px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
            >
              <FiClock className="inline mr-2" /> Procesar Expirados
            </button>
          </div>

          {/* Cuentas Gratuitas Expiradas */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Cuentas Gratuitas</h4>
            <div className="flex justify-between items-center bg-red-50 p-3 rounded-md">
              <p className="text-sm text-red-800">Cuentas gratuitas expiradas pendientes de limpieza.</p>
              <span className="text-xl font-bold text-red-900">{freeStats.expired}</span>
            </div>
            <button
              onClick={() => handleAction('/api/admin/cleanup-expired-free', 'Limpieza de cuentas iniciada.')}
              className="mt-2 w-full text-sm px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              <FiTrash2 className="inline mr-2" /> Limpiar Expiradas
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
