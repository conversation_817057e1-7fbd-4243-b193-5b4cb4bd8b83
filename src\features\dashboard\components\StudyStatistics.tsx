import React, { useEffect, useState } from 'react';
import { EstadisticasEstudio, obtenerEstadisticasDetalladas } from '../../../lib/supabase';

interface StudyStatisticsProps {
  coleccionId: string;
  onClose: () => void;
}

export default function StudyStatistics({ coleccionId, onClose }: StudyStatisticsProps) {
  const [estadisticas, setEstadisticas] = useState<EstadisticasEstudio | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState<'general' | 'progreso' | 'dificiles'>('general');

  useEffect(() => {
    const cargarEstadisticas = async () => {
      setIsLoading(true);
      try {
        const data = await obtenerEstadisticasDetalladas(coleccionId);
        setEstadisticas(data);
      } catch (error) {
        console.error('Error al cargar estadísticas:', error);
        setError('No se pudieron cargar las estadísticas detalladas');
      } finally {
        setIsLoading(false);
      }
    };

    cargarEstadisticas();
  }, [coleccionId]);

  // Formatear la fecha para mostrarla en la lista
  const formatearFecha = (fechaStr: string): string => {
    const fecha = new Date(fechaStr);
    return fecha.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Calcular el porcentaje para las barras de progreso
  const calcularPorcentaje = (valor: number, total: number): number => {
    if (total === 0) return 0;
    return Math.round((valor / total) * 100);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold">Estadísticas detalladas de estudio</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="border-b">
          <div className="flex">
            <button
              onClick={() => setActiveTab('general')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'general'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              General
            </button>
            <button
              onClick={() => setActiveTab('progreso')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'progreso'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Progreso
            </button>
            <button
              onClick={() => setActiveTab('dificiles')}
              className={`px-4 py-2 font-medium ${
                activeTab === 'dificiles'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Tarjetas difíciles
            </button>
          </div>
        </div>

        <div className="p-4 overflow-y-auto flex-grow">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
            </div>
          ) : error ? (
            <div className="text-red-500 text-center py-4">{error}</div>
          ) : !estadisticas ? (
            <div className="text-gray-500 text-center py-4">No hay datos estadísticos disponibles</div>
          ) : (
            <>
              {activeTab === 'general' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Sesiones de estudio</h3>
                      <p className="text-3xl font-bold text-orange-600">{estadisticas.totalSesiones}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Total de revisiones</h3>
                      <p className="text-3xl font-bold text-orange-600">{estadisticas.totalRevisiones}</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">Promedio por sesión</h3>
                      <p className="text-3xl font-bold text-orange-600">
                        {estadisticas.totalSesiones > 0
                          ? Math.round(estadisticas.totalRevisiones / estadisticas.totalSesiones)
                          : 0}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">Distribución de respuestas</h3>
                    <div className="space-y-2">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Difícil</span>
                          <span className="text-sm font-medium">
                            {estadisticas.distribucionDificultad.dificil} ({calcularPorcentaje(
                              estadisticas.distribucionDificultad.dificil,
                              estadisticas.totalRevisiones
                            )}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-red-500 h-2.5 rounded-full"
                            style={{
                              width: `${calcularPorcentaje(
                                estadisticas.distribucionDificultad.dificil,
                                estadisticas.totalRevisiones
                              )}%`
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Normal</span>
                          <span className="text-sm font-medium">
                            {estadisticas.distribucionDificultad.normal} ({calcularPorcentaje(
                              estadisticas.distribucionDificultad.normal,
                              estadisticas.totalRevisiones
                            )}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-yellow-500 h-2.5 rounded-full"
                            style={{
                              width: `${calcularPorcentaje(
                                estadisticas.distribucionDificultad.normal,
                                estadisticas.totalRevisiones
                              )}%`
                            }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Fácil</span>
                          <span className="text-sm font-medium">
                            {estadisticas.distribucionDificultad.facil} ({calcularPorcentaje(
                              estadisticas.distribucionDificultad.facil,
                              estadisticas.totalRevisiones
                            )}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-green-500 h-2.5 rounded-full"
                            style={{
                              width: `${calcularPorcentaje(
                                estadisticas.distribucionDificultad.facil,
                                estadisticas.totalRevisiones
                              )}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'progreso' && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Progreso a lo largo del tiempo</h3>
                  {estadisticas.progresoTiempo.length === 0 ? (
                    <div className="text-gray-500 text-center py-4">No hay datos de progreso disponibles</div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Fecha
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Nuevas
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Aprendiendo
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Repasando
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Aprendidas
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {estadisticas.progresoTiempo.map((progreso, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {formatearFecha(progreso.fecha)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                  {progreso.nuevas}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                  {progreso.aprendiendo}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span className="px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                                  {progreso.repasando}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                  {progreso.aprendidas}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'dificiles' && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Tarjetas más difíciles</h3>
                  {estadisticas.tarjetasMasDificiles.length === 0 ? (
                    <div className="text-gray-500 text-center py-4">No hay datos suficientes para determinar las tarjetas más difíciles</div>
                  ) : (
                    <div className="space-y-4">
                      {estadisticas.tarjetasMasDificiles.map((tarjeta) => (
                        <div key={tarjeta.id} className="border rounded-lg p-4 hover:bg-gray-50">
                          <p className="font-medium mb-2">{tarjeta.pregunta}</p>
                          <div className="flex space-x-4 text-sm">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                              <span>Difícil: {tarjeta.dificil}</span>
                            </div>
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
                              <span>Normal: {tarjeta.normal}</span>
                            </div>
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                              <span>Fácil: {tarjeta.facil}</span>
                            </div>
                          </div>
                          <div className="mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-1.5 flex">
                              <div
                                className="bg-red-500 h-1.5 rounded-l-full"
                                style={{
                                  width: `${calcularPorcentaje(tarjeta.dificil, tarjeta.totalRevisiones)}%`
                                }}
                              ></div>
                              <div
                                className="bg-yellow-500 h-1.5"
                                style={{
                                  width: `${calcularPorcentaje(tarjeta.normal, tarjeta.totalRevisiones)}%`
                                }}
                              ></div>
                              <div
                                className="bg-green-500 h-1.5 rounded-r-full"
                                style={{
                                  width: `${calcularPorcentaje(tarjeta.facil, tarjeta.totalRevisiones)}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
