interface ProcessingStateProps {
  planId: string;
  timeElapsed: number;
}

export function ProcessingState({ planId, timeElapsed }: ProcessingStateProps) {
  const planNames = {
    'free': 'Plan Gratuito',
    'usuario': 'Plan Usuario', 
    'pro': 'Plan Pro'
  };

  const progress = Math.min((timeElapsed / 30) * 100, 90);
  const estimatedTime = Math.max(30 - timeElapsed, 5);

  return (
    <div className="text-center">
      {/* Spinner animado */}
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      
      <h2 className="text-2xl font-bold text-gray-900 mb-2">
        ¡Pago Confirmado!
      </h2>
      
      <p className="text-lg text-gray-700 mb-4">
        Creando tu cuenta para el <strong>{planNames[planId as keyof typeof planNames] || planId}</strong>
      </p>
      
      {/* Mensaje clave que elimina confusión */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <p className="text-blue-800 text-sm">
          <strong>🤖 Tu cuenta se está creando automáticamente</strong><br/>
          No necesitas hacer nada más. Tiempo estimado: ~{estimatedTime} segundos
        </p>
      </div>
      
      {/* Barra de progreso */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-1000"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      
      {/* Indicador de pasos */}
      <div className="text-xs text-gray-600 mb-4">
        <div className="flex justify-between items-center">
          <span className={timeElapsed >= 0 ? 'text-green-600 font-medium' : ''}>
            ✓ Pago procesado
          </span>
          <span className={timeElapsed >= 10 ? 'text-green-600 font-medium' : timeElapsed >= 5 ? 'text-blue-600 font-medium' : ''}>
            {timeElapsed >= 10 ? '✓' : timeElapsed >= 5 ? '⏳' : '○'} Creando usuario
          </span>
          <span className={timeElapsed >= 20 ? 'text-green-600 font-medium' : timeElapsed >= 15 ? 'text-blue-600 font-medium' : ''}>
            {timeElapsed >= 20 ? '✓' : timeElapsed >= 15 ? '⏳' : '○'} Configurando plan
          </span>
          <span className={timeElapsed >= 30 ? 'text-green-600 font-medium' : timeElapsed >= 25 ? 'text-blue-600 font-medium' : ''}>
            {timeElapsed >= 30 ? '✓' : timeElapsed >= 25 ? '⏳' : '○'} Finalizando
          </span>
        </div>
      </div>
      
      <p className="text-sm text-gray-600">
        No cierres esta ventana. Serás redirigido automáticamente.
      </p>
    </div>
  );
}
