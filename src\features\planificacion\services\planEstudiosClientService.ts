'use client';

import { supabase } from '@/lib/supabase/client';
import { obtenerUsuarioActual } from '@/lib/supabase/authService';
import { PlanEstudios, ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';
import { PlanEstudiosEstructurado } from './planGeneratorService';

/**
 * Obtiene el plan de estudios activo para un temario (versión para cliente)
 */
export async function obtenerPlanEstudiosActivoCliente(temarioId: string): Promise<PlanEstudios | null> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return null;
    }

    // Primero verificar que el temario existe
    const { data: temarioData, error: temarioError } = await supabase
      .from('temarios')
      .select('id')
      .eq('id', temarioId)
      .eq('user_id', user.id)
      .single();

    if (temarioError) {
      if (temarioError.code === 'PGRST116') {
        console.log('Temario no encontrado:', temarioId);
        return null;
      }
      console.error('Error al verificar temario:', temarioError);
      return null;
    }

    if (!temarioData) {
      console.log('Temario no encontrado:', temarioId);
      return null;
    }

    const { data, error } = await supabase
      .from('planes_estudios')
      .select('*')
      .eq('user_id', user.id)
      .eq('temario_id', temarioId)
      .eq('activo', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No hay plan activo
      }
      console.error('Error al obtener plan activo:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener plan activo:', error);
    return null;
  }
}

/**
 * Obtiene el progreso de un plan de estudios (versión para cliente)
 */
export async function obtenerProgresoPlaneCliente(planId: string): Promise<ProgresoPlanEstudios[]> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return [];
    }

    const { data, error } = await supabase
      .from('progreso_plan_estudios')
      .select('*')
      .eq('plan_id', planId)
      .eq('user_id', user.id)
      .order('semana_numero', { ascending: true })
      .order('creado_en', { ascending: true });

    if (error) {
      console.error('Error al obtener progreso del plan:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener progreso del plan:', error);
    return [];
  }
}

/**
 * Guarda el progreso de una tarea del plan (versión para cliente)
 */
export async function guardarProgresoTareaCliente(
  planId: string,
  semanaNúmero: number,
  diaNombre: string,
  tareaTitulo: string,
  tareaTipo: 'estudio' | 'repaso' | 'practica' | 'evaluacion',
  completado: boolean,
  tiempoRealMinutos?: number,
  notasProgreso?: string,
  calificacion?: number
): Promise<boolean> {
  try {
    const { user, error: authError } = await obtenerUsuarioActual();
    if (!user || authError) {
      return false;
    }

    // Verificar si ya existe un registro de progreso para esta tarea
    let query = supabase
      .from('progreso_plan_estudios')
      .select('id')
      .eq('plan_id', planId)
      .eq('user_id', user.id)
      .eq('semana_numero', semanaNúmero)
      .eq('dia_nombre', diaNombre)
      .eq('tarea_titulo', tareaTitulo);



    const { data: existente } = await query.single();

    if (existente) {
      // Actualizar registro existente
      const { error } = await supabase
        .from('progreso_plan_estudios')
        .update({
          completado,
          fecha_completado: completado ? new Date().toISOString() : null,
          tiempo_real_minutos: tiempoRealMinutos,
          notas_progreso: notasProgreso,
          calificacion,
          actualizado_en: new Date().toISOString()
        })
        .eq('id', existente.id);

      if (error) {
        console.error('Error al actualizar progreso:', error);
        return false;
      }
    } else {
      // Crear nuevo registro
      const { error } = await supabase
        .from('progreso_plan_estudios')
        .insert([{
          plan_id: planId,
          user_id: user.id,
          semana_numero: semanaNúmero,
          dia_nombre: diaNombre,
          tarea_titulo: tareaTitulo,
          tarea_tipo: tareaTipo,
          completado,
          fecha_completado: completado ? new Date().toISOString() : null,
          tiempo_real_minutos: tiempoRealMinutos,
          notas_progreso: notasProgreso,
          calificacion
        }]);

      if (error) {
        console.error('Error al crear progreso:', error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error al guardar progreso de tarea:', error);
    return false;
  }
}

/**
 * Obtiene estadísticas del progreso del plan (versión para cliente)
 */
export async function obtenerEstadisticasProgresoCliente(planId: string): Promise<{
  totalTareas: number;
  tareasCompletadas: number;
  porcentajeCompletado: number;
  tiempoTotalEstimado: number;
  tiempoTotalReal: number;
  semanasCompletadas: number;
  totalSemanas: number;
}> {
  try {
    const progreso = await obtenerProgresoPlaneCliente(planId);
    const plan = await supabase
      .from('planes_estudios')
      .select('plan_data')
      .eq('id', planId)
      .single();

    if (!plan.data) {
      return {
        totalTareas: 0,
        tareasCompletadas: 0,
        porcentajeCompletado: 0,
        tiempoTotalEstimado: 0,
        tiempoTotalReal: 0,
        semanasCompletadas: 0,
        totalSemanas: 0
      };
    }

    const planData = plan.data.plan_data as PlanEstudiosEstructurado;
    const totalSemanas = planData.semanas.length;
    
    // Calcular total de tareas
    let totalTareas = 0;
    planData.semanas.forEach(semana => {
      semana.dias.forEach(dia => {
        totalTareas += dia.tareas.length;
      });
    });

    const tareasCompletadas = progreso.filter(p => p.completado).length;
    const porcentajeCompletado = totalTareas > 0 ? (tareasCompletadas / totalTareas) * 100 : 0;
    
    const tiempoTotalReal = progreso
      .filter(p => p.tiempo_real_minutos)
      .reduce((total, p) => total + (p.tiempo_real_minutos || 0), 0);

    // Calcular semanas completadas (todas las tareas de la semana completadas)
    let semanasCompletadas = 0;
    planData.semanas.forEach(semana => {
      const tareasSemanaTotales = semana.dias.reduce((total, dia) => total + dia.tareas.length, 0);
      const tareasSemanCompletadas = progreso.filter(p => 
        p.semana_numero === semana.numero && p.completado
      ).length;
      
      if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {
        semanasCompletadas++;
      }
    });

    return {
      totalTareas,
      tareasCompletadas,
      porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,
      tiempoTotalEstimado: 0, // Se puede calcular desde el plan
      tiempoTotalReal,
      semanasCompletadas,
      totalSemanas
    };
  } catch (error) {
    console.error('Error al obtener estadísticas de progreso:', error);
    return {
      totalTareas: 0,
      tareasCompletadas: 0,
      porcentajeCompletado: 0,
      tiempoTotalEstimado: 0,
      tiempoTotalReal: 0,
      semanasCompletadas: 0,
      totalSemanas: 0
    };
  }
}
