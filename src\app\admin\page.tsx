'use client';

import React, { useEffect, useState } from 'react';
import NotificationStatsWidget from '@/features/admin/components/NotificationStatsWidget';
import AccountManagementWidget from '@/features/admin/components/AccountManagementWidget';
import UserLookupWidget from '@/features/admin/components/UserLookupWidget';

export default function AdminDashboardPage() {
  // Aquí podrías cargar estadísticas generales si lo deseas en el futuro
  const [loading, setLoading] = useState(false);

  if (loading) {
    return <div>Cargando datos del dashboard...</div>;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-gray-900 mb-6">
        Dashboard de Administración
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Columna Izquierda */}
        <div className="space-y-6">
          <NotificationStatsWidget />
          <UserLookupWidget />
        </div>

        {/* Columna Derecha */}
        <div className="space-y-6">
          <AccountManagementWidget />
          {/* Aquí se pueden añadir más widgets en el futuro */}
        </div>
      </div>
    </div>
  );
}
