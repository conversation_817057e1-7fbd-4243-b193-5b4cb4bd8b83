// src/lib/services/email/types.ts
// Tipos e interfaces compartidas para el sistema de notificaciones por email

export interface EmailNotification {
  to: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  type: 'subscription_cancelled' | 'grace_period_ending' | 'plan_expired' | 'payment_failed' | 'welcome' | 'plan_upgrade_confirmation' | 'admin_security_alert' | 'other';
  userId?: string;
  metadata?: Record<string, any>;
}

export interface EmailTemplate {
  htmlContent: string;
  textContent: string;
  subject: string;
}

export interface EmailStats {
  byType: Record<string, number>;
  byStatus: Record<string, number>;
  total: number;
  recentNotifications: any[];
}

export interface FailureStats {
  totalFailures: number;
  failureRate: number;
  errorsByType: Record<string, number>;
  recentFailures: any[];
}

export interface UserNotificationsResult {
  notifications: any[];
  total: number;
}

export interface RetryResult {
  attempted: number;
  successful: number;
  failed: number;
  errors: string[];
}

export interface EmailLogData {
  recipient_email: string;
  subject: string;
  type: string;
  sent_at: string;
  status: string;
  user_id?: string;
  metadata?: Record<string, any>;
}

export interface EmailUpdateData {
  status: string;
  updated_at: string;
  delivered_at?: string;
  metadata?: Record<string, any>;
}

export type EmailStatus = 'pending' | 'sent' | 'failed' | 'retried_successfully' | 'delivered' | 'bounced';

export type EmailType = 'subscription_cancelled' | 'grace_period_ending' | 'plan_expired' | 'payment_failed' | 'welcome' | 'other';

export type ErrorCategory = 'Network Error' | 'Invalid Email' | 'Rate Limit' | 'Authentication Error' | 'Email Bounced' | 'Other Error';

export interface SecurityAlertData {
  payerEmail: string;
  accountOwnerEmail: string;
  requestedPlan: string;
  currentPlan?: string;
  paymentAmount: number;
  currency: string;
  stripeSessionId: string;
  timestamp: string;
  actionTaken: string;
}
