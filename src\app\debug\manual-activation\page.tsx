'use client';

import { useState } from 'react';

export default function ManualActivationPage() {
  const [sessionId, setSessionId] = useState('cs_test_b10TWN4YmqV8Pd6B5I9bl5S9OzgdJ9MJG9ZpOuuyDoCk76YRpgBP0SnGBX');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleActivation = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug/manual-activation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId })
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: 'Error en la petición' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">Activación Manual de Usuario</h1>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Session ID de Stripe:
            </label>
            <input
              type="text"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="cs_test_..."
            />
          </div>

          <button
            onClick={handleActivation}
            disabled={loading || !sessionId}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Procesando...' : 'Activar Usuario Manualmente'}
          </button>

          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-2">Resultado:</h3>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
