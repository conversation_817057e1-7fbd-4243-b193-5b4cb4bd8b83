/**
 * Tipos específicos para el sistema de calendario del plan de estudios
 */

import { PlanEstudiosEstructurado, TareaPlan, SemanaPlan, DiaPlan } from '../services/planGeneratorService';
import { ProgresoPlanEstudios } from '@/lib/supabase/supabaseClient';

/**
 * Estado de un día en el calendario
 */
export type EstadoDiaCalendario = 
  | 'normal'           // Día sin tareas
  | 'con-tareas'       // Día con tareas pendientes
  | 'completado'       // Día con todas las tareas completadas
  | 'parcial'          // Día con algunas tareas completadas
  | 'hoy'              // Día actual
  | 'fuera-mes';       // Día que no pertenece al mes actual

/**
 * Información de un día en el calendario
 */
export interface DiaCalendario {
  fecha: Date;
  dia: number;
  estaEnMesActual: boolean;
  esHoy: boolean;
  estado: EstadoDiaCalendario;
  tareas: TareaDelDia[];
  totalTareas: number;
  tareasCompletadas: number;
  porcentajeCompletado: number;
}

/**
 * Información de una tarea específica de un día
 */
export interface TareaDelDia {
  tarea: TareaPlan;
  semanaNumero: number;
  diaNombre: string;
  completada: boolean;
  fechaCompletado?: string;
}

/**
 * Datos procesados del plan para el calendario
 */
export interface DatosPlanCalendario {
  fechaInicio: Date;
  fechaFin: Date;
  totalSemanas: number;
  mapaDias: Map<string, DiaCalendario>; // key: YYYY-MM-DD
  rangoFechas: {
    minYear: number;
    maxYear: number;
    minMonth: number;
    maxMonth: number;
  };
}

/**
 * Estado del calendario
 */
export interface EstadoCalendario {
  yearActual: number;
  mesActual: number; // 0-11
  fechaSeleccionada: Date | null;
  fechasCalendario: Date[];
  diasCalendario: DiaCalendario[];
}

/**
 * Props para el componente de calendario
 */
export interface PlanCalendarioProps {
  plan: PlanEstudiosEstructurado;
  progresoPlan: ProgresoPlanEstudios[];
  fechaSeleccionada?: Date | null;
  onFechaSeleccionada: (fecha: Date) => void;
  onMesChanged?: (year: number, month: number) => void;
  className?: string;
}

/**
 * Props para el panel de tareas del día
 */
export interface TareasDelDiaProps {
  fecha: Date | null;
  tareas: TareaDelDia[];
  isLoading?: boolean;
  onTareaClick?: (tarea: TareaDelDia) => void;
  onTareaCompletadaChange?: (tarea: TareaDelDia, completada: boolean) => Promise<void>;
  className?: string;
}

/**
 * Configuración del calendario
 */
export interface ConfiguracionCalendario {
  primerDiaSemana: 0 | 1; // 0 = Domingo, 1 = Lunes
  mostrarSemanasFueraDelMes: boolean;
  resaltarDiaActual: boolean;
  permitirSeleccionFutura: boolean;
  permitirSeleccionPasada: boolean;
  formatoFecha: string;
  locale: string;
}

/**
 * Resultado del procesamiento del plan para el calendario
 */
export interface PlanProcesado {
  datosPlan: DatosPlanCalendario;
  estadisticas: {
    totalTareas: number;
    tareasCompletadas: number;
    porcentajeGeneral: number;
    diasConTareas: number;
    diasCompletados: number;
  };
  errores: string[];
}

/**
 * Filtros para las tareas del día
 */
export interface FiltrosTareasDelDia {
  soloCompletadas?: boolean;
  soloPendientes?: boolean;
  tipoTarea?: TareaPlan['tipo'];
  busqueda?: string;
}

/**
 * Evento de navegación del calendario
 */
export interface EventoNavegacionCalendario {
  yearAnterior: number;
  mesAnterior: number;
  yearNuevo: number;
  mesNuevo: number;
  direccion: 'anterior' | 'siguiente' | 'directo';
}

/**
 * Evento de selección de día
 */
export interface EventoSeleccionDia {
  fecha: Date;
  diaCalendario: DiaCalendario;
  esNuevaSeleccion: boolean;
  fechaAnterior: Date | null;
}

/**
 * Hook para el manejo del calendario
 */
export interface UsePlanCalendarioReturn {
  // Estado
  estadoCalendario: EstadoCalendario;
  datosPlan: DatosPlanCalendario | null;
  isLoading: boolean;
  error: string | null;
  
  // Acciones
  navegarMes: (direccion: 'anterior' | 'siguiente') => void;
  irAMes: (year: number, month: number) => void;
  seleccionarFecha: (fecha: Date) => void;
  irAHoy: () => void;
  
  // Utilidades
  obtenerTareasDelDia: (fecha: Date) => TareaDelDia[];
  obtenerEstadoDia: (fecha: Date) => EstadoDiaCalendario;
  esFechaSeleccionable: (fecha: Date) => boolean;
  
  // Datos computados
  tituloMes: string;
  tareasDelDiaSeleccionado: TareaDelDia[];
  estadisticasDelDia: {
    total: number;
    completadas: number;
    porcentaje: number;
  } | null;
}

/**
 * Opciones para el procesamiento del plan
 */
export interface OpcionesProcesamiento {
  incluirDiasSinTareas?: boolean;
  calcularEstadisticas?: boolean;
  validarFechas?: boolean;
  ordenarTareasPorTipo?: boolean;
}

/**
 * Resultado de validación de fechas del plan
 */
export interface ValidacionFechasPlan {
  esValido: boolean;
  errores: string[];
  advertencias: string[];
  fechasProblematicas: {
    semana: number;
    fecha: string;
    problema: string;
  }[];
}
