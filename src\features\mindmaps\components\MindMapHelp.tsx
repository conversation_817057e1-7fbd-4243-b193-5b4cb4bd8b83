import React, { useState } from 'react';

interface MindMapHelpProps {
  className?: string;
}

export default function MindMapHelp({ className = '' }: MindMapHelpProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`relative ${className}`}>
      {/* Botón de ayuda */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors"
        title="Ayuda sobre mapas mentales"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
        </svg>
        ¿Cómo usar?
      </button>

      {/* Modal de ayuda */}
      {isOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsOpen(false)}
          />
          {/* Modal */}
          <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 sm:w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 max-h-80 sm:max-h-96 overflow-y-auto">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-gray-800">Guía de Mapas Mentales</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-2 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-700 mb-1">📝 Generar</h4>
                <p className="text-xs">Describe el mapa mental basado en tus documentos.</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-1">🔍 Vista Previa</h4>
                <p className="text-xs">Revisa el resultado antes de expandir.</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-1">🖥️ Pantalla Completa</h4>
                <p className="text-xs">Botón azul para mejor visualización.</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-1">⌨️ Controles</h4>
                <ul className="list-disc list-inside space-y-0.5 ml-2 text-xs">
                  <li><kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">ESC</kbd> para salir</li>
                  <li>Clic fuera para cerrar</li>
                  <li>Zoom y pan en el mapa</li>
                  <li>Clic en nodos para expandir</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-1">💾 Descargar</h4>
                <p className="text-xs">Guarda como archivo HTML interactivo.</p>
              </div>
            </div>

            <div className="pt-2 border-t border-gray-100">
              <p className="text-xs text-gray-500">
                💡 <strong>Consejo:</strong> Los mapas son interactivos con zoom y navegación.
              </p>
            </div>
          </div>
          </div>
        </>
      )}
    </div>
  );
}
