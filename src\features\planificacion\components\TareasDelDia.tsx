/**
 * Componente para mostrar las tareas de un día seleccionado en el calendario
 */

import React, { useState } from 'react';
import {
  <PERSON>Check,
  FiClock,
  FiBook,
  FiRefreshCw,
  FiTarget,
  FiPlay,
  FiCalendar,
  FiAlertCircle,
  FiExternalLink,
  FiLoader
} from 'react-icons/fi';
import { TareasDelDiaProps, TareaDelDia } from '../types/calendarTypes';
import { formatearFechaDisplay } from '@/lib/utils/dateUtils';

const TareasDelDia: React.FC<TareasDelDiaProps> = ({
  fecha,
  tareas,
  isLoading = false,
  onTareaClick,
  onTareaCompletadaChange,
  className = ''
}) => {

  // Estado para manejar tareas que están siendo actualizadas
  const [tareasActualizando, setTareasActualizando] = useState<Set<string>>(new Set());

  // Obtener icono según el tipo de tarea
  const obtenerIconoTarea = (tipo: string) => {
    switch (tipo) {
      case 'estudio':
        return <FiBook className="w-4 h-4" />;
      case 'repaso':
        return <FiRefreshCw className="w-4 h-4" />;
      case 'practica':
        return <FiPlay className="w-4 h-4" />;
      case 'evaluacion':
        return <FiTarget className="w-4 h-4" />;
      default:
        return <FiClock className="w-4 h-4" />;
    }
  };

  // Obtener color según el tipo de tarea
  const obtenerColorTarea = (tipo: string) => {
    switch (tipo) {
      case 'estudio':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'repaso':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'practica':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'evaluacion':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Obtener texto del tipo de tarea
  const obtenerTextoTipo = (tipo: string) => {
    switch (tipo) {
      case 'estudio':
        return 'Estudio';
      case 'repaso':
        return 'Repaso';
      case 'practica':
        return 'Práctica';
      case 'evaluacion':
        return 'Evaluación';
      default:
        return 'Tarea';
    }
  };

  // Calcular estadísticas del día
  const estadisticas = React.useMemo(() => {
    if (!tareas.length) return null;
    
    const completadas = tareas.filter(t => t.completada).length;
    const total = tareas.length;
    const porcentaje = Math.round((completadas / total) * 100);
    
    return { completadas, total, porcentaje };
  }, [tareas]);

  // Manejar clic en tarea
  const handleTareaClick = (tarea: TareaDelDia) => {
    if (onTareaClick) {
      onTareaClick(tarea);
    }
  };



  // Manejar cambio de estado de completado
  const handleCompletadoChange = async (tarea: TareaDelDia, completada: boolean) => {
    if (!onTareaCompletadaChange) return;

    const tareaId = `${tarea.semanaNumero}-${tarea.diaNombre}-${tarea.tarea.titulo}`;

    try {
      // Marcar como actualizando
      setTareasActualizando(prev => new Set(prev).add(tareaId));

      // Llamar a la función de actualización
      await onTareaCompletadaChange(tarea, completada);

    } catch (error) {
      console.error('Error al actualizar tarea:', error);
      // El error se maneja en el componente padre
    } finally {
      // Quitar de la lista de actualizando
      setTareasActualizando(prev => {
        const newSet = new Set(prev);
        newSet.delete(tareaId);
        return newSet;
      });
    }
  };

  // Verificar si una tarea está siendo actualizada
  const estaActualizando = (tarea: TareaDelDia): boolean => {
    const tareaId = `${tarea.semanaNumero}-${tarea.diaNombre}-${tarea.tarea.titulo}`;
    return tareasActualizando.has(tareaId);
  };

  if (!fecha) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 ${className}`}>
        <div className="text-center text-gray-500">
          <FiCalendar className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-xs sm:text-sm">Selecciona un día en el calendario</p>
          <p className="text-xs text-gray-400 mt-1 hidden sm:block">
            para ver las tareas programadas
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm calendario-slide-in ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 px-3 sm:px-4 py-2 sm:py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-semibold text-gray-900 text-sm sm:text-base">
              {formatearFechaDisplay(fecha)}
            </h4>
            {estadisticas && (
              <p className="text-xs sm:text-sm text-gray-600">
                {estadisticas.completadas} de {estadisticas.total} tareas
                <span className="hidden sm:inline"> completadas</span>
              </p>
            )}
          </div>
          {estadisticas && (
            <div className="text-right">
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                estadisticas.porcentaje === 100 
                  ? 'bg-green-100 text-green-800'
                  : estadisticas.porcentaje > 0
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {estadisticas.porcentaje}%
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Contenido */}
      <div className="p-3 sm:p-4">
        {isLoading ? (
          // Estado de carga
          <div className="space-y-3">
            {Array.from({ length: 3 }, (_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gray-200 rounded" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : tareas.length === 0 ? (
          // Sin tareas
          <div className="text-center py-6">
            <FiAlertCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-500">No hay tareas programadas</p>
            <p className="text-xs text-gray-400 mt-1">
              para este día
            </p>
          </div>
        ) : (
          // Lista de tareas
          <div className="space-y-3">
            {tareas.map((tareaDelDia, index) => {
              const actualizando = estaActualizando(tareaDelDia);

              return (
                <div
                  key={index}
                  className={`border rounded-lg p-3 calendario-estado-transition ${
                    tareaDelDia.completada
                      ? 'bg-green-50 border-green-200'
                      : 'bg-white border-gray-200'
                  } ${actualizando ? 'opacity-75' : ''}`}
                >
                <div className="flex items-start space-x-3">
                  {/* Checkbox funcional */}
                  {onTareaCompletadaChange ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCompletadoChange(tareaDelDia, !tareaDelDia.completada);
                      }}
                      disabled={actualizando}
                      className={`flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center transition-all hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${
                        tareaDelDia.completada
                          ? 'bg-green-500 border-green-500 hover:bg-green-600'
                          : 'border-gray-300 hover:border-gray-400'
                      } ${actualizando ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      aria-label={`${tareaDelDia.completada ? 'Desmarcar' : 'Marcar'} como completada: ${tareaDelDia.tarea.titulo}`}
                      title={`${tareaDelDia.completada ? 'Desmarcar' : 'Marcar'} como completada`}
                    >
                      {actualizando ? (
                        <FiLoader className="w-3 h-3 text-gray-400 animate-spin" />
                      ) : tareaDelDia.completada ? (
                        <FiCheck className="w-3 h-3 text-white" />
                      ) : null}
                    </button>
                  ) : (
                    // Checkbox visual (no funcional)
                    <div className={`flex-shrink-0 w-5 h-5 rounded border-2 mt-0.5 flex items-center justify-center ${
                      tareaDelDia.completada
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300'
                    }`}>
                      {tareaDelDia.completada && (
                        <FiCheck className="w-3 h-3 text-white" />
                      )}
                    </div>
                  )}

                  {/* Contenido de la tarea */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      {/* Tipo de tarea */}
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
                        obtenerColorTarea(tareaDelDia.tarea.tipo)
                      }`}>
                        {obtenerIconoTarea(tareaDelDia.tarea.tipo)}
                        <span className="ml-1">{obtenerTextoTipo(tareaDelDia.tarea.tipo)}</span>
                      </div>

                      {/* Duración */}
                      {tareaDelDia.tarea.duracionEstimada && (
                        <div className="flex items-center text-xs text-gray-500">
                          <FiClock className="w-3 h-3 mr-1" />
                          {tareaDelDia.tarea.duracionEstimada}
                        </div>
                      )}
                    </div>

                    {/* Título */}
                    <h5 className={`font-medium text-sm ${
                      tareaDelDia.completada 
                        ? 'text-green-800 line-through' 
                        : 'text-gray-900'
                    }`}>
                      {tareaDelDia.tarea.titulo}
                    </h5>

                    {/* Descripción */}
                    {tareaDelDia.tarea.descripcion && (
                      <p className={`text-xs mt-1 ${
                        tareaDelDia.completada 
                          ? 'text-green-600' 
                          : 'text-gray-600'
                      }`}>
                        {tareaDelDia.tarea.descripcion}
                      </p>
                    )}

                    {/* Información adicional */}
                    <div className="flex items-center justify-between mt-2">
                      <div className="text-xs text-gray-500">
                        Semana {tareaDelDia.semanaNumero} • {tareaDelDia.diaNombre}
                      </div>
                      
                      {tareaDelDia.completada && tareaDelDia.fechaCompletado && (
                        <div className="text-xs text-green-600">
                          ✓ Completada
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Botón de navegación */}
                  {onTareaClick && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTareaClick(tareaDelDia);
                      }}
                      className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                      aria-label={`Ir a esta tarea en el plan: ${tareaDelDia.tarea.titulo}`}
                      title="Ir al plan de estudios"
                    >
                      <FiExternalLink className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer con acciones (si hay tareas) */}
      {tareas.length > 0 && !isLoading && (
        <div className="bg-gray-50 px-4 py-2 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>
              {tareas.filter(t => t.completada).length} completadas
            </span>
            <span>
              {tareas.filter(t => !t.completada).length} pendientes
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TareasDelDia;
